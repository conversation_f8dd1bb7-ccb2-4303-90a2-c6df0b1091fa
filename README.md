# FastAPI 项目启动指南

本文档说明如何在本地环境下安装依赖并启动 FastAPI 服务。

## 前置条件

1. 已安装 [Python 3.10+](https://www.python.org/)
2. 建议使用虚拟环境（`venv` 或 `conda`）

## 本地开发环境搭建

### 1. 克隆项目
```bash
git clone <your-repo-url>
cd <project-name>
```

### 2. 创建虚拟环境
```bash
# Windows
python -m venv venv
venv\Scripts\activate

# macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

### 3. 安装依赖
```bash
cd app
pip install -r requirements.txt
```

### 4. 启动服务
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

## 访问地址

- API文档: http://127.0.0.1:8000/docs
- ReDoc文档: http://127.0.0.1:8000/redoc
- 健康检查: http://127.0.0.1:8000/health

## 常见问题

- 如遇到端口占用，请指定其他端口：`--port 8080`
- 如果修改代码后界面未自动刷新，确认是否已添加 `--reload` 参数
- 虚拟环境激活失败，请检查Python版本和路径

## 部署说明

项目支持Docker部署和Matrix平台部署，详见部署文档。