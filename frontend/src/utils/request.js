import axios from 'axios';
import { ElMessage } from 'element-plus';

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  }
});

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 开发环境打印请求信息
    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
    }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    // 统一错误处理
    const message = error.response?.data?.detail || error.message || '请求失败';
    ElMessage.error(message);

    if (import.meta.env.VITE_APP_ENV === 'development') {
      console.error('API请求失败:', error);
    }

    return Promise.reject(error);
  }
);

export default request;