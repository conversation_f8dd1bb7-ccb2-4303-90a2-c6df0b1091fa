import request from '@/utils/request';

// 业务相关API
export const businessAPI = {
  // 获取业务信息
  getBusinessInfo: (domain) => request.get(`/businesses/${domain}`),

  // 获取业务列表
  getBusinessList: () => request.get('/businesses'),

  // 创建业务
  createBusiness: (data) => request.post('/businesses', data),

  // 更新业务
  updateBusiness: (domain, data) => request.put(`/businesses/${domain}`, data),
};

// 报告相关API
export const reportAPI = {
  // 获取报告列表
  getReports: (params) => request.get('/reports', { params }),

  // 创建报告
  createReport: (data) => request.post('/reports', data),

  // 更新报告
  updateReport: (id, data) => request.put(`/reports/${id}`, data),

  // 删除报告
  deleteReport: (id, params) => request.delete(`/reports/${id}`, { params }),

  // 获取报告详情
  getReportInfo: (domain, id, params) =>
    request.get(`/get_report_info/${domain}/${id}`, { params }),
};