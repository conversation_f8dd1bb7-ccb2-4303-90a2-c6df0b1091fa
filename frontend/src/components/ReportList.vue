<template>
  <div>

    <!-- 顶部导航按钮 -->
    <div class="top-bar">
      <span class="top-bar-label">业务域：</span>
      <el-select
        v-model="currentDomain"
        @change="handleDomainChange"
        size="large"
        style="width: 260px;"
      >
        <el-option
          v-for="item in domainOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <!-- 新增/编辑业务按钮 -->
      <el-button type="primary" size="large" @click="openBusinessDialog">新增业务</el-button>
      <el-button type="success" size="large" @click="openEditBusinessDialog">编辑当前业务</el-button>
    </div>

    <!-- 新增业务对话框 -->
    <el-dialog v-model="businessDialogVisible" title="业务配置" width="600px">
      <el-form :model="businessForm" label-width="120px">
        <el-form-item label="业务域 (英文)">
          <el-input v-model="businessForm.domain" placeholder="如 browser" :disabled="isEditBusiness"></el-input>
        </el-form-item>
        <el-form-item label="业务中文名称">
          <el-input v-model="businessForm.chinese_name" placeholder="浏览器"></el-input>
        </el-form-item>
        <el-form-item label="Dataflow API">
          <el-input v-model="businessForm.dataflow_api" placeholder="https://..."></el-input>
        </el-form-item>
        <el-form-item label="job_id列表">
          <div class="job-id-container">
            <div v-for="(jobId, index) in businessForm.job_ids_array" :key="index" class="job-id-item">
              <el-input v-model="businessForm.job_ids_array[index]" placeholder="输入job_id" style="width: 200px;"></el-input>
              <el-button type="danger" size="small" @click="removeJobId(index)" :disabled="businessForm.job_ids_array.length <= 1">-</el-button>
            </div>
            <el-button type="primary" size="small" @click="addJobId">+</el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="指标名称配置">
          <div class="columns-config">
            <div v-for="jobId in businessForm.job_ids_array" :key="jobId" class="job-columns">
              <div class="job-header">
                <span class="job-title">[{{ jobId }}] :</span>
              </div>
              <div class="columns-list">
                <div class="column-item">
                  <el-input v-model="businessForm.columns_dict[jobId]" placeholder="指标名称" style="width: 150px;"></el-input>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        
        <el-form-item label="指标中文名配置">
          <div class="columns-config">
            <div v-for="jobId in businessForm.job_ids_array" :key="jobId" class="job-columns">
              <div class="job-header">
                <span class="job-title">[{{ jobId }}] :</span>
              </div>
              <div class="columns-list">
                <div class="column-item">
                  <el-input v-model="businessForm.columns_cn_dict[jobId]" placeholder="指标中文名" style="width: 150px;"></el-input>
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="场景类型">
          <el-input v-model="businessForm.type_list" placeholder="env1,env2"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="businessDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitBusiness">提交</el-button>
      </template>
    </el-dialog>

    <h1>{{ domainTitle }}</h1>

    <!-- 配置按钮 -->
    <div style="text-align: right; margin-bottom: 20px;margin-right :30%">
      <el-button type="primary" @click="openConfigDialog">配置</el-button>
    </div>

    <div>
      <!-- 不再直接调用 fetchExperiments，而只是更新 searchQuery 的值 -->
      <el-input v-model="searchQuery" placeholder="搜索实验" style="width: 300px" ></el-input>
      <!-- 点击搜索按钮时调用 fetchExperiments 并且 page 设为 1 -->
      <el-button type="primary" @click="handleSearch">搜索</el-button>
    </div>

    <el-table :data="experiments" border style="width: 70%;margin: auto;margin-top: 20px">
    <el-table-column prop="id" label="ID" width="70"></el-table-column>
    <el-table-column prop="name" label="报告名称" width="120">
      <template #default="scope">
        <el-link @click="goToDetail(scope.row.id)" class="clickable-link">
          {{ scope.row.name }}
        </el-link>
      </template>
    </el-table-column>
    <el-table-column prop="creator" label="创建人" width="120"></el-table-column>
    <el-table-column prop="aa" label="AA实验周期" width="150"></el-table-column>
    <el-table-column prop="ab" label="AB实验周期" width="180"></el-table-column>
    <el-table-column label="报告状态" width="100">
      <template #default="scope">
        <el-tooltip :content="getStatusMeta(scope.row).label" placement="top">
          <el-icon :color="getStatusMeta(scope.row).color">
            <component :is="getStatusMeta(scope.row).icon" />
          </el-icon>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column prop="expId" label="实验ID" width="150"></el-table-column>
    <el-table-column prop="controlId" label="对照ID" width="150"></el-table-column>
    <el-table-column prop="userSize" label="用户规模" width="100"></el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <el-button size="mini" type="primary" @click="openCopyDialog(scope.row)">复制</el-button>
        <el-button size="mini" type="primary" @click="openEditDialog(scope.row)">编辑</el-button>
        <el-button size="mini" type="danger" @click="handleDelete(scope.row.id,currentDomain)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>


    <!-- 分页控件 -->
    <el-pagination
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalExperiments"
      :page-size="pageSize"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
      style="margin-top: 20px;">
    </el-pagination>

    <el-dialog title="配置实验报告" v-model="configDialogVisible" width="600px">
    <el-form :model="formData" label-width="100px" label-position="right" class="dialog-form">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="报告名称">
            <el-input v-model="formData.name"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="用户量级">
            <el-input v-model="formData.user_size"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="实验组ID">
            <el-input v-model="formData.experiment_id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="对照组ID">
            <el-input v-model="formData.control_id"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="AA开始时间">
            <el-date-picker
              v-model="formData.AA_start"
              type="date"
              format="YYYYMMDD"
              value-format="YYYYMMDD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="AA结束时间">
            <el-date-picker
              v-model="formData.AA_end"
              type="date"
              format="YYYYMMDD"
              value-format="YYYYMMDD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">

        <el-col :span="12">
          <el-form-item label="AB开始时间">
            <el-date-picker
              v-model="formData.AB_start"
              type="date"
              format="YYYYMMDD"
              placeholder="选择开始日期"
              value-format="YYYYMMDD"
            ></el-date-picker>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="AB结束时间">
            <el-date-picker
              v-model="formData.AB_end"
              type="date"
              format="YYYYMMDD"
              placeholder="选择结束日期"
              value-format="YYYYMMDD"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注">
        <el-input type="textarea" v-model="formData.notes" :rows="3"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
        <span class="dialog-footer">
          <el-button @click="configDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitConfig">提交</el-button>
        </span>
      </template>
    </el-dialog>


    <!-- 同步结果弹窗 -->
    <el-dialog
      v-model="resultDialogVisible"
      :title="resultTitle"
      width="400px"
      :close-on-click-modal="false"
      :show-close="true">
      <div>{{ resultMessage }}</div>
      <template #footer>
        <el-button type="primary" @click="resultDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>


  </div>
</template>

<script>
import { businessAPI, reportAPI } from '@/api';
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElLoading, ElMessageBox, ElMessage } from 'element-plus';
import { Clock, Loading, CircleCheckFilled, QuestionFilled } from '@element-plus/icons-vue';

export default {
  setup() {
    const router = useRouter();
    const route = useRoute();
    const experiments = ref([]);
    const searchQuery = ref('');
    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalExperiments = ref(0);
    const currentDomain = ref(route.query.domain || 'browser');

    // 监听地址栏 domain 变化
    watch(
      () => route.query.domain,
      (newVal) => {
        if (newVal && newVal !== currentDomain.value) {
          currentDomain.value = newVal;
          currentPage.value = 1;
          fetchExperiments();
        }
      }
    );

    // 当下拉框改变 currentDomain 时，立即刷新实验列表
    watch(currentDomain, () => {
      currentPage.value = 1;
      fetchExperiments();
    });

    const domainOptions = ref([]);

    const fetchDomains = async () => {
      try {
        const res = await businessAPI.getBusinessList();
        domainOptions.value = res.data.map((biz) => ({
          value: biz.domain,
          label: biz.chinese_domain_name || biz.domain
        }));

        if (!domainOptions.value.find((o) => o.value === currentDomain.value) && domainOptions.value.length) {
          currentDomain.value = domainOptions.value[0].value;
        }
      } catch (err) {
        console.error('获取业务域失败', err);
      }
    };

    const domainTitle = computed(() => {
      const found = domainOptions.value.find((o) => o.value === currentDomain.value);
      const displayName = found ? found.label : currentDomain.value;
      return `${displayName}实验报告列表`;
    });

    const configDialogVisible = ref(false);
    const formData = ref({
      name: '',
      experiment_id: '',
      control_id: '',
      AA_start: '',
      AA_end: '',
      AB_start: '',
      AB_end: '',
      user_size: '',
      notes: '',
    });

    const parsePeriod = (period) => {
      if (!period) return ["", ""];
      const parts = period.split("-");
      return [parts[0] || "", parts[1] || ""];
    };

    const openEditDialog = (report) => {
      const [aaStart, aaEnd] = parsePeriod(report.aa);
      const [abStart, abEnd] = parsePeriod(report.ab);
      formData.value = {
        name: report.name,
        experiment_id: report.expId,
        control_id: report.controlId,
        AA_start: aaStart,
        AA_end: aaEnd,
        AB_start: abStart,
        AB_end: abEnd,
        user_size: report.userSize,
        notes: report.notes,
        id: report.id
      };
      configDialogVisible.value = true;
    };

    const openCopyDialog = (report) => {
      const [aaStart, aaEnd] = parsePeriod(report.aa);
      const [abStart, abEnd] = parsePeriod(report.ab);
      formData.value = {
        name: report.name,
        experiment_id: report.expId,
        control_id: report.controlId,
        AA_start: aaStart,
        AA_end: aaEnd,
        AB_start: abStart,
        AB_end: abEnd,
        user_size: report.userSize,
        notes: report.notes,
      };
      configDialogVisible.value = true;
    };

    // 获取实验报告列表
    const fetchExperiments = async (page = 1) => {
      const skip = (page - 1) * pageSize.value;
      try {
        const params = { skip, limit: pageSize.value, domain: currentDomain.value };
        if (searchQuery.value.trim()) params.query = searchQuery.value.trim();

        const response = await reportAPI.getReports(params);
        experiments.value = response.data.items;
        totalExperiments.value = response.data.total;
      } catch (error) {
        console.error('Error fetching reports:', error);
      }
    };

    onMounted(() => {
      fetchDomains();
      document.title = domainTitle.value;
      fetchExperiments(currentPage.value);
    });

    watch(domainTitle, (newTitle) => {
      document.title = newTitle;
    });

    const handlePageChange = (page) => {
      currentPage.value = page;
      fetchExperiments(page);
    };

    const handleSizeChange = (size) => {
      pageSize.value = size;
      currentPage.value = 1;
      fetchExperiments();
    };

    const handleSearch = () => {
      currentPage.value = 1;
      fetchExperiments(currentPage.value);
    };

    const goToDetail = (id) => {
      const newWindow = window.open();
      const url = router.resolve({ name: 'ReportInfo', params: { id }, query: { domain: currentDomain.value } }).href;
      newWindow.location.href = url;
    };

    const openConfigDialog = () => {
      configDialogVisible.value = true;
    };

    const submitConfig = async () => {
      if (!formData.value.name.trim() || !formData.value.experiment_id.trim() ||
          !formData.value.control_id.trim() || !formData.value.AA_start ||
          !formData.value.AA_end || !formData.value.AB_start || !formData.value.AB_end ||
          !formData.value.user_size.trim()) {
        ElMessage.error('请完整填写所有字段');
        return;
      }

      const payload = {
        name: formData.value.name,
        domain: currentDomain.value,
        creator: 'wuweixin',
        aa: `${formData.value.AA_start}-${formData.value.AA_end}`,
        ab: `${formData.value.AB_start}-${formData.value.AB_end}`,
        expId: formData.value.experiment_id,
        controlId: formData.value.control_id,
        userSize: formData.value.user_size,
        notes: formData.value.notes
      };

      try {
        if (formData.value.id) {
          await reportAPI.updateReport(formData.value.id, payload);
          ElMessage.success('更新成功');
        } else {
          await reportAPI.createReport(payload);
          ElMessage.success('提交成功');
        }

        configDialogVisible.value = false;
        fetchExperiments();
        resetFormData();
      } catch (error) {
        // 错误已在拦截器中处理
        console.error('操作失败:', error);
      }
    };

    const resetFormData = () => {
      formData.value = {
        name: '',
        experiment_id: '',
        control_id: '',
        AA_start: '',
        AA_end: '',
        AB_start: '',
        AB_end: '',
        user_size: '',
        notes: '',
        id: ''
      };
    };

    const handleDelete = async (id) => {
      try {
        const confirmed = await ElMessageBox.confirm(
          '此操作将永久删除该实验报告，是否继续？',
          '警告',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        );

        if (confirmed) {
          await reportAPI.deleteReport(id, { domain: currentDomain.value });
          ElMessage.success('删除成功');
          fetchExperiments(currentPage.value);
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('Error deleting report:', error);
        } else {
          ElMessage.info('删除已取消');
        }
      }
    };

    const setDomain = (domain) => {
      if (domain === route.query.domain) return;
      router.replace({ query: { ...route.query, domain } });
    };

    const handleDomainChange = (val) => {
      setDomain(val);
    };

    // 业务配置相关
    const businessDialogVisible = ref(false);
    const isEditBusiness = ref(false);
    const businessForm = ref({
      domain: '',
      dataflow_api: '',
      job_ids_array: [''],
      columns_dict: {},
      columns_cn_dict: {},
      chinese_name: '',
      type_list: ''
    });

    const openBusinessDialog = () => {
      isEditBusiness.value = false;
      businessForm.value = {
        domain: '',
        dataflow_api: '',
        job_ids_array: [''],
        columns_dict: {},
        columns_cn_dict: {},
        chinese_name: '',
        type_list: ''
      };
      businessDialogVisible.value = true;
    };

    const addJobId = () => {
      businessForm.value.job_ids_array.push('');
    };

    const removeJobId = (index) => {
      if (businessForm.value.job_ids_array.length > 1) {
        const removedJobId = businessForm.value.job_ids_array[index];
        businessForm.value.job_ids_array.splice(index, 1);

        if (businessForm.value.columns_dict[removedJobId]) {
          delete businessForm.value.columns_dict[removedJobId];
        }
        if (businessForm.value.columns_cn_dict[removedJobId]) {
          delete businessForm.value.columns_cn_dict[removedJobId];
        }
      }
    };

    const submitBusiness = async () => {
      if (!businessForm.value.domain.trim()) {
        ElMessage.error('业务域不能为空');
        return;
      }

      const validJobIds = businessForm.value.job_ids_array.filter(id => id.trim());
      if (validJobIds.length === 0) {
        ElMessage.error('至少需要一个job_id');
        return;
      }

      try {
        const cleanColumnsDict = {};
        const cleanColumnsCnDict = {};

        validJobIds.forEach(jobId => {
          if (businessForm.value.columns_dict[jobId]) {
            const columnValue = businessForm.value.columns_dict[jobId];
            if (typeof columnValue === 'string') {
              cleanColumnsDict[jobId] = columnValue.split(',').map(s => s.trim()).filter(Boolean);
            } else if (Array.isArray(columnValue)) {
              cleanColumnsDict[jobId] = columnValue.filter(col => col.trim());
            }
          }

          if (businessForm.value.columns_cn_dict[jobId]) {
            const columnCnValue = businessForm.value.columns_cn_dict[jobId];
            if (typeof columnCnValue === 'string') {
              cleanColumnsCnDict[jobId] = columnCnValue.split(',').map(s => s.trim()).filter(Boolean);
            } else if (Array.isArray(columnCnValue)) {
              cleanColumnsCnDict[jobId] = columnCnValue.filter(col => col.trim());
            }
          }
        });

        const payload = {
          domain: businessForm.value.domain.trim(),
          dataflow_api: businessForm.value.dataflow_api.trim(),
          job_id_dict: validJobIds,
          columns_dict: cleanColumnsDict,
          chinese_domain_name: businessForm.value.chinese_name.trim(),
          columns_chinese_dict: cleanColumnsCnDict,
          type: businessForm.value.type_list.split(',').map(s=>s.trim()).filter(Boolean)
        };

        if (isEditBusiness.value) {
          await businessAPI.updateBusiness(businessForm.value.domain, payload);
          ElMessage.success('更新业务成功');
        } else {
          await businessAPI.createBusiness(payload);
          ElMessage.success('新增业务成功');
        }

        businessDialogVisible.value = false;
        isEditBusiness.value = false;
        fetchDomains();
      } catch (e) {
        // 错误已在拦截器中处理
        console.error('业务操作失败:', e);
      }
    };

    const openEditBusinessDialog = async () => {
      try {
        const res = await businessAPI.getBusinessInfo(currentDomain.value);
        const biz = res.data;

        const jobIdsArray = Array.isArray(biz.job_id_dict) ? biz.job_id_dict : Object.keys(biz.job_id_dict || {});

        businessForm.value = {
          domain: biz.domain,
          dataflow_api: biz.dataflow_api,
          job_ids_array: jobIdsArray.length > 0 ? jobIdsArray : [''],
          columns_dict: biz.columns_dict || {},
          columns_cn_dict: biz.columns_chinese_dict || {},
          chinese_name: biz.chinese_domain_name || '',
          type_list: Array.isArray(biz.type) ? biz.type.join(',') : ''
        };
        isEditBusiness.value = true;
        businessDialogVisible.value = true;
      } catch (e) {
        // 错误已在拦截器中处理
        console.error('获取业务信息失败:', e);
      }
    };

    return {
      experiments,
      searchQuery,
      currentPage,
      pageSize,
      totalExperiments,
      configDialogVisible,
      formData,
      fetchExperiments,
      handlePageChange,
      handleSizeChange,
      handleSearch,
      goToDetail,
      openConfigDialog,
      submitConfig,
      openEditDialog,
      getStatusMeta,
      handleDelete,
      openCopyDialog,
      currentDomain,
      setDomain,
      domainTitle,
      domainOptions,
      handleDomainChange,
      businessDialogVisible,
      businessForm,
      openBusinessDialog,
      submitBusiness,
      openEditBusinessDialog,
      isEditBusiness,
      addJobId,
      removeJobId
    };
  }
};
</script>

<style scoped>
.el-pagination {
  justify-content: center;
}

.dialog-form .el-form-item {
  margin-bottom: 15px;
}
.dialog-footer {
  text-align: right;
}
.el-input {
  width: 100%;
}
.clickable-link {
  color: #409eff; /* 蓝色以提示点击性 */
  text-decoration: underline; /* 添加下划线 */
}

.clickable-link:hover {
  color: #66b1ff; /* 悬停时的颜色 */
}

/* 顶部业务域选择区域放大 */
.top-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
  font-size: 18px;
}

.top-bar-label {
  font-weight: bold;
}

.top-bar .el-select {
  font-size: 14px;
}

.top-bar .el-button {
  font-size: 14px;
  padding: 6px 12px;
}

/* 业务配置对话框样式 */
.job-id-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.job-id-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.columns-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
}

.job-columns {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  background-color: #fafafa;
}

.job-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e4e7ed;
}

.job-title {
  font-weight: bold;
  color: #606266;
}

.columns-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.column-item {
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
