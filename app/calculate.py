import httpx
from fastapi import HTTPException
import json
import pandas as pd
import numpy as np
from .schemas import DataFrameSchema
from scipy.stats import ttest_ind
from typing import Tuple, Dict, List
from .format import df_formatter, df_formatter_2, df_formatter_3


job_id_dict = {
    "browser": ["browser_push","browser_push_grouptype"],
    "mivideo": ["mivideo_push"]
}
columns_dict = {
    "browser" :{
        "browser_push": ["date", "job_id", "type", "exp_id", "expose_pv", "expose_uv", "click_pv", "click_uv","pv_ctr","uv_ctr"],
        "browser_push_grouptype": ["date", "job_id", "type", "exp_id","grouptype", "expose_pv", "expose_uv", "click_pv", "click_uv","pv_ctr","uv_ctr"]
    },
    "mivideo": {
        "mivideo_push": ["date", "job_id", "type", "exp_id", "expose_pv", "expose_uv", "click_pv", "click_uv","pv_ctr","uv_ctr","ad_expose","ad_click","fee"],
    }
}

columns_chinese_dict = {
    "browser" :{
        "browser_push": ["日期", "job_id", "type", "exp_id", "曝光pv", "曝光uv", "点击pv", "点击uv","pv点击率","uv点击率"],
        "browser_push_grouptype": ["日期", "job_id", "type", "exp_id","用户分层", "曝光pv", "曝光uv", "点击pv", "点击uv","pv点击率","uv点击率"]
    },
    "mivideo": {
        "mivideo_push": ["日期", "job_id", "type", "exp_id", "曝光pv", "曝光uv", "点击pv", "点击uv","pv点击率","uv点击率","广告曝光","广告点击","收入"]
    }
}


# API 默认值（可被业务配置覆盖）
DEFAULT_API_MI_URL = "https://api-dataflow.dt.mi.com/openapi/v1/service/ASUQOFFIHMNA_abtest_report"

def _ttest(series_exp: pd.Series, series_ctl: pd.Series) -> float:
    """Welch t-test；样本不足 2 条时返回 1"""
    if len(series_exp) < 2 or len(series_ctl) < 2:
        return 1.0
    return ttest_ind(series_exp, series_ctl, equal_var=False).pvalue

def _metric_block(
    exp_daily: pd.Series,
    ctl_daily: pd.Series,
) -> Dict[str, float]:
    """返回 AB 均值 / 增量 / 增幅 / 极值 / 正负比"""
    diff = exp_daily - ctl_daily

    exp_mean = exp_daily.mean()
    ctl_mean = ctl_daily.mean()
    abs_inc = exp_mean - ctl_mean
    rel_inc = abs_inc / ctl_mean * 100 if ctl_mean else 0

    # 去掉 0 后再求极值，避免“全 0”时 NaN
    diff_no0 = diff[diff != 0]
    max_inc = diff_no0.max() if not diff_no0.empty else 0
    min_inc = diff_no0.min() if not diff_no0.empty else 0
    pos_neg = f"{(diff > 0).sum()}:{(diff < 0).sum()}"

    return {
        "实验均值": exp_mean,
        "对照均值": ctl_mean,
        "平均增量": abs_inc,
        "平均增幅": rel_inc,
        "差值最大": max_inc,
        "差值最小": min_inc,
        "正负比": pos_neg,
    }


async def fetchMiReport(api_url: str, payload):
    """调用小米 Dataflow 开放接口，将原始结果透传给前端。若需要鉴权，请将 appid/secret 配置为环境变量。"""

    headers = {
        "Content-Type": "application/json",
    }
    
    async with httpx.AsyncClient(timeout=30) as client:
        try:
            resp = await client.post(api_url, json=payload, headers=headers)
            resp.raise_for_status()
        except httpx.HTTPError as e:
            raise HTTPException(status_code=502, detail=f"请求小米API失败: {e}")
    return resp.json()

async def abtest_report_df(report, biz_conf: dict,type:str,scene:str):
    # 将 report 信息转成 fetchMiReport 所需 payload
    # 解析 AB 字符串 "YYYYMMDD-YYYYMMDD"
    aa_start, aa_end = (report.aa.split("-") + ["", ""])[:2]
    ab_start, ab_end = (report.ab.split("-") + ["", ""])[:2]
    # 使用业务配置中的 API URL，没有则用默认
    api_url = biz_conf.get("dataflow_api", DEFAULT_API_MI_URL)
    job_id_mapping = biz_conf["job_id_dict"]
    columns_mapping = biz_conf["columns_dict"]
    columns_chinese_mapping = biz_conf["columns_chinese_dict"]
    max_length = max(len(columns_mapping[job_id]) for job_id in job_id_mapping)

    # 如果 report.exp_id 包含 "-"，则将其按 "-" 分割并用 "," 连接，否则直接使用原值
    expIdStr = ",".join(map(str.strip, report.expId.split("-"))) if "-" in report.expId else report.expId
    controlIdStr = ",".join(map(str.strip, report.controlId.split("-"))) if "-" in report.controlId else report.controlId
    expIdList = expIdStr.split(",")
    controlIdList = controlIdStr.split(",")
    select_columns = ["date", "job_id","type", "exp_id"] + [f"c{i}" for i in range(1, max_length+1)]
    payload = {
        "select": select_columns,
        "sort": "",
        "filterList": [
            {"name": "date", "operation": "区间", "value": json.dumps({"min": aa_start, "max": ab_end})},
            {"name": "exp_id", "operation": "包含", "value": expIdStr+","+controlIdStr},
            {"name": "type", "operation": "等于", "value": scene}
        ]
    }
    
    data = await fetchMiReport(api_url, payload)
    df = pd.DataFrame(data["data"])
    # 举例：只取部分列 & 重命名
    select_columns = ["date", "job_id","type", "exp_id"] + [f"c{i}" for i in range(1, max_length+1)]
    df = df.loc[:, select_columns]
    

    # job_id_mapping 可以是列表或字典（若是字典则取 keys 顺序）
    if isinstance(job_id_mapping, dict):
        job_ids_to_iter = list(job_id_mapping.keys())
    else:
        job_ids_to_iter = job_id_mapping

    df_jobs = []
    for job_id in job_ids_to_iter:
        df_job = df[df["job_id"] == job_id.replace("_groupby","")]
        col_list = columns_mapping[job_id]
        df_job = df_job.iloc[:,:len(col_list)]
        df_job.columns = col_list


        if type == "merged":
            if "groupby" in job_id:
                df_job_cal = await abtest_report_df_groupby(df_job, exp_group_ids=expIdList, control_group_ids=controlIdList, ab_start_date=ab_start, ab_end_date=ab_end,report=report,job_id=job_id,columns_chinese_mapping=columns_chinese_mapping)
            else:
                df_job_cal = df_job.drop(columns=["job_id","type"])
                df_job_cal = await abtest_report_df_normal(df_job_cal, exp_ids=expIdList, ctl_ids=controlIdList, aa_start=aa_start, aa_end=aa_end, ab_start=ab_start, ab_end=ab_end,report=report,job_id=job_id,columns_chinese_mapping=columns_chinese_mapping)
            df_jobs.extend(df_job_cal)
        else:
            for exp_id in expIdList:
                if "groupby" in job_id:
                    df_job_cal = await abtest_report_df_groupby(df_job, exp_group_ids=[exp_id], control_group_ids=controlIdList, ab_start_date=ab_start, ab_end_date=ab_end,report=report,job_id=job_id,columns_chinese_mapping=columns_chinese_mapping)
                else:
                    df_job_cal = df_job.drop(columns=["job_id","type"])
                    df_job_cal = await abtest_report_df_normal(df_job_cal, exp_ids=[exp_id], ctl_ids=controlIdList, aa_start=aa_start, aa_end=aa_end, ab_start=ab_start, ab_end=ab_end,report=report,job_id=job_id,columns_chinese_mapping=columns_chinese_mapping)

                df_jobs.extend(df_job_cal)
    return df_jobs

async def abtest_report_df_groupby(
    data: 'pd.DataFrame',
    *,
    exp_group_ids: list[str] | list[int],
    control_group_ids: list[str] | list[int],
    ab_start_date: str,  # 格式 YYYYMMDD
    ab_end_date: str,    # 格式 YYYYMMDD
    report,
    job_id,
    columns_chinese_mapping,
) -> 'pd.DataFrame':
    
    groupby_column = job_id.split("_")[-1]


    required_cols = {
        'date', 'exp_id', f'{groupby_column}',
        'expose_pv', 'expose_uv', 'click_pv', 'click_uv','pv_ctr','uv_ctr'
    }

    missing = required_cols - set(data.columns)
    if missing:
        raise ValueError(f"数据缺少列: {missing}")

    # 将除了 'date', 'exp_id', groupby_column 的列转换为 float 类型
    colsToFloat = [col for col in data.columns if col not in ["date", "exp_id", groupby_column,"job_id","type"]]
    try:
        data[colsToFloat] = data[colsToFloat].apply(lambda col: col.astype(float))
    except Exception as e:
        raise ValueError(f"列类型转换为 float 失败: {e}")

    exp_data_ab = data.loc[data['exp_id'].isin(exp_group_ids)].copy()
    control_data_ab = data.loc[data['exp_id'].isin(control_group_ids)].copy()

    # 去除无用列
    drop_cols = ['exp_id', 'date','job_id','type']
    df_exp_group = exp_data_ab.drop(columns=drop_cols).groupby(groupby_column).mean().reset_index()
    df_ctl_group = control_data_ab.drop(columns=drop_cols).groupby(groupby_column).mean().reset_index()


    # 准备相对 / 绝对提升
    groupby_column_metrics = df_exp_group[groupby_column]
    exp_metrics = df_exp_group.drop(columns=[groupby_column])
    ctl_metrics = df_ctl_group.drop(columns=[groupby_column])

    relative_inc = (exp_metrics - ctl_metrics) / ctl_metrics * 100
    absolute_inc = exp_metrics - ctl_metrics

    for frame in (exp_metrics, ctl_metrics, relative_inc, absolute_inc):
        frame.insert(0, groupby_column, groupby_column_metrics)

    exp_metrics.insert(0, 'group', '实验组')
    ctl_metrics.insert(0, 'group', '对照组')
    relative_inc.insert(0, 'group', '相对增长')
    absolute_inc.insert(0, 'group', '绝对增长')

    # concat
    metrics_per_group = pd.concat([exp_metrics, ctl_metrics, absolute_inc, relative_inc], ignore_index=True)

    metrics_per_group[groupby_column] = metrics_per_group[groupby_column].map({
        'group_1': '轻度用户',
        'group_2': '中度用户',
        'group_3': '重度用户'
    }).fillna(metrics_per_group[groupby_column])
    metrics_per_group = df_formatter_3(metrics_per_group,report,job_id,columns_chinese_mapping)
    metrics_per_group = [
        DataFrameSchema(
            name=f"{','.join(exp_group_ids)}-用户分层数据",
                note=None,
                columns=metrics_per_group.columns.tolist(),
                data=metrics_per_group.to_dict(orient="records"),
                is_merged=True,
                is_split=True,
            )]
    return metrics_per_group

async def abtest_report_df_normal(
    data: pd.DataFrame,
    *,
    exp_ids: List[int],
    ctl_ids: List[int],
    aa_start: int,
    aa_end: int,
    ab_start: int,
    ab_end: int,
    report,
    job_id,
    columns_chinese_mapping,
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    aa_start = int(aa_start)
    aa_end = int(aa_end)
    ab_start = int(ab_start)
    ab_end = int(ab_end)



    # 检查 data 的每一列的数据类型，并输出到日志，便于调试
    mask_exp = data["exp_id"].isin(exp_ids)
    mask_ctl = data["exp_id"].isin(ctl_ids)

    data = data.astype(float)
    exp_ab = data[mask_exp & (data["date"].between(ab_start, ab_end))].copy()
    ctl_ab = data[mask_ctl & (data["date"].between(ab_start, ab_end))].copy()

    exp_aa = data[mask_exp & (data["date"].between(aa_start, aa_end))].copy()
    ctl_aa = data[mask_ctl & (data["date"].between(aa_start, aa_end))].copy()


    # 按日期聚合（日均）
    def daily_mean(df: pd.DataFrame) -> pd.DataFrame:
        return df.groupby("date").mean(numeric_only=True).reset_index()

    exp_ab_d, ctl_ab_d = daily_mean(exp_ab), daily_mean(ctl_ab)
    exp_aa_d, ctl_aa_d = daily_mean(exp_aa), daily_mean(ctl_aa)

    # --- 循环各指标 --------------------------------------------------------
    out: Dict[str, Dict[str, float]] = {}
    
    for metric in exp_ab_d.columns[2:]:
        ab_block = _metric_block(exp_ab_d[metric], ctl_ab_d[metric])
        aa_block = _metric_block(exp_aa_d[metric], ctl_aa_d[metric])

        out[metric] = {
            "指标": metric,
            # AB
            **{f"AB{key}": val for key, val in ab_block.items()},
            "AB_pvalue": _ttest(exp_ab_d[metric], ctl_ab_d[metric]),
            # AA
            **{f"AA{key}": val for key, val in aa_block.items()},
            "AA_pvalue": _ttest(exp_aa_d[metric], ctl_aa_d[metric]),
        }

    metrics_df = pd.DataFrame(out).T
    metrics_df = metrics_df.drop(columns=['AA差值最大','AA差值最小','AA正负比'])
    metrics_df = df_formatter(metrics_df,report,job_id,columns_chinese_mapping)
    # --- 按天细分 ----------------------------------------------------------
    metrics_per_date = date_metrics(exp_ab, ctl_ab)
    metrics_per_date = df_formatter_2(metrics_per_date,report,job_id,columns_chinese_mapping)
    metrics_df = DataFrameSchema(
            name=f"{','.join(exp_ids)}-实验组[汇总]数据",
            note=None,
            columns=metrics_df.columns.tolist(),
            data=metrics_df.to_dict(orient="records"),
            is_merged=False,
            is_split=True,
        )
    metrics_per_date = DataFrameSchema(
            name=f"{','.join(exp_ids)}-实验组[每日]数据",
            note=None,
            columns=metrics_per_date.columns.tolist(),
            data=metrics_per_date.to_dict(orient="records"),
            is_merged=True,
            is_split=True,
        )
    return [metrics_df, metrics_per_date]

def date_metrics(exp_data_ab,control_data_ab):
    import pandas as pd

    # 计算该报表的每日细分指标
    # 按日期排序并重设索引，确保数据对齐
    df1_sorted = exp_data_ab.sort_values(by='date').reset_index(drop=True)
    df2_sorted = control_data_ab.sort_values(by='date').reset_index(drop=True)

    # 删除 'date' 和 'exp_id' 列以计算相对提升和绝对增长
    df1_sorted = df1_sorted.drop(columns=['exp_id'])
    df2_sorted = df2_sorted.drop(columns=['exp_id'])

    df1_sorted = df1_sorted.groupby('date').mean(numeric_only=True).reset_index()
    df2_sorted = df2_sorted.groupby('date').mean(numeric_only=True).reset_index()

    date = df1_sorted['date']
    # 删除 'date' 和 'exp_id' 列以计算相对提升和绝对增长
    df1_sorted = df1_sorted.drop(columns=['date'])
    df2_sorted = df2_sorted.drop(columns=['date'])


    # 计算相对提升值
    relative_increase = (df1_sorted - df2_sorted) / df2_sorted * 100

    # 计算绝对增长值
    absolute_increase = df1_sorted - df2_sorted

    # 将 date 列重新添加到结果中
    df1_sorted.insert(0, 'date', date)
    df2_sorted.insert(0, 'date', date)
    relative_increase.insert(0, 'date', date)
    absolute_increase.insert(0, 'date', date)


    # 添加 group 列来标识数据来源
    df1_sorted.insert(0, 'group', '实验组')
    df2_sorted.insert(0, 'group', '对照组')
    relative_increase.insert(0, 'group', '相对增长')
    absolute_increase.insert(0, 'group', '绝对增长')

    # 合并实验组、对照组、相对增长和绝对增长数据
    metrics_per_date = pd.concat([df1_sorted, df2_sorted, absolute_increase, relative_increase], ignore_index=True)

    return metrics_per_date
