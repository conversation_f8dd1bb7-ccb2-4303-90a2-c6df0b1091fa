import logging
import logging.config
import os
from datetime import datetime
import json

# 日志配置
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'detailed': {
            'format': '[%(asctime)s] %(levelname)s [%(name)s.%(funcName)s:%(lineno)d] %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'json': {
            'format': '%(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'default',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'DEBUG',
            'formatter': 'detailed',
            'filename': 'logs/app.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'encoding': 'utf8'
        },
        'error_file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': 'ERROR',
            'formatter': 'detailed',
            'filename': 'logs/error.log',
            'maxBytes': 10485760,
            'backupCount': 5,
            'encoding': 'utf8'
        }
    },
    'loggers': {
        '': {  # root logger
            'level': 'DEBUG',
            'handlers': ['console', 'file', 'error_file']
        },
        'uvicorn': {
            'level': 'INFO',
            'handlers': ['console', 'file'],
            'propagate': False
        },
        'sqlalchemy.engine': {
            'level': 'WARNING',  # 避免SQL日志过多
            'handlers': ['file'],
            'propagate': False
        }
    }
}


def setup_logging():
    """初始化日志配置"""
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)

    # 应用配置
    logging.config.dictConfig(LOGGING_CONFIG)

    return logging.getLogger(__name__)


# 创建结构化日志记录器
class StructuredLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)

    def log_request(self, method: str, url: str, status_code: int, duration: float, **kwargs):
        """记录请求日志"""
        log_data = {
            'type': 'request',
            'method': method,
            'url': str(url),
            'status_code': status_code,
            'duration': round(duration, 6),
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        self.logger.info(json.dumps(log_data, ensure_ascii=False))

    def log_db_operation(self, operation: str, table: str, duration: float, **kwargs):
        """记录数据库操作日志"""
        log_data = {
            'type': 'database',
            'operation': operation,
            'table': table,
            'duration': round(duration, 6),
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        self.logger.info(json.dumps(log_data, ensure_ascii=False))

    def log_error(self, error: Exception, context: str = None, **kwargs):
        """记录错误日志"""
        log_data = {
            'type': 'error',
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        self.logger.error(json.dumps(log_data, ensure_ascii=False))